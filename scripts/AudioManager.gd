extends Node

# AudioManager - <PERSON>ton pre správu audio systému v hre "Prekliate Dedičstvo"
# Spravuje background music, SFX, crossfade efekty a volume kontroly

# Audio players
var background_music: AudioStreamPlayer
var sfx_player: AudioStreamPlayer
var ui_player: AudioStreamPlayer
var voice_player: AudioStreamPlayer
var storm_player: AudioStreamPlayer  # Špeciálny player pre storm efekt
var atmosphere_player: AudioStreamPlayer  # Player pre atmosférické zvuky (brána, vstupná hala)

# Audio Resources - priamo načítavanie MP3 súborov
var audio_tracks = {
	"main_menu": "res://audio/music/MainTheme.mp3",
	"storm_journey": "res://audio/music/2. Storm Journey - Búrlivá cesta.mp3",
	"forest_shadows": "res://audio/music/3. Forest of Shadows - Temný les.mp3",
	"castle_gates": "res://audio/music/4. Castle Gates - Brána hrôzy.mp3",
	"viktor_theme": "res://audio/music/5. <PERSON>'s Theme - V<PERSON><PERSON> služobník.mp3",
	"library_secrets": "res://audio/music/6. Library of Secrets - Tajomná knižnica.mp3",
	"puzzle_theme": "res://audio/music/7. Puzzle Theme - Hádanky.mp3",
	"alchemy_lab": "res://audio/music/10. Alchemy Laboratory - Alchýmia.mp3",
	"descent_darkness": "res://audio/music/11. Descent into Darkness - Zostup do katakomb.mp3",
	"ancient_crypts": "res://audio/music/12. Ancient Crypts - Pradávne hrobky.mp3",
	"isabelle_awakening": "res://audio/music/13. Isabelle's Awakening - Prebudenie zla.mp3",
	"final_ritual": "res://audio/music/14. Final Ritual - Posledný rituál.mp3",
	"van_helsing_rescue": "res://audio/music/15. Van Helsing 15. Rescued - Záchrana mentora.mp3"
}

# Jednoduchý systém pre pamätanie predchádzajúcej hudby
var scene_music_map = {
	1: "storm_journey",      # Kapitola 1
	2: "castle_gates",       # Kapitola 2
	3: "library_secrets",    # Kapitola 3
	4: "alchemy_lab",        # Kapitola 4
	5: "descent_darkness",   # Kapitola 5
	6: "isabelle_awakening", # Kapitola 6
	7: "van_helsing_rescue"  # Epilóg
}

# Volume settings
var music_volume = 0.504  # Znížené o 20% + 10% + 30% z 1.0 (0.8 * 0.9 * 0.7 = 0.504)
var sfx_volume = 0.8
var ui_volume = 0.6
var voice_volume = 0.7  # Znížené o 30% (1.0 * 0.7 = 0.7)

# Current track tracking
var current_track = ""
var previous_track = ""

# Atmospheric sounds tracking
var current_atmosphere = ""
var atmosphere_volume = 0.7

# Crossfade settings
var crossfade_time = 2.0
var is_crossfading = false

func _ready():
	print("🎵 AudioManager inicializovaný")
	setup_audio_players()
	setup_audio_buses()
	load_audio_settings()

	# Reset crossfade flag
	is_crossfading = false

	# Aplikovať novú hlasitosť na aktuálne hrajúcu hudbu
	apply_current_music_volume()

	# Main menu hudba sa spúšťa len v MainMenu.gd, nie automaticky

func setup_audio_players():
	"""Vytvorí audio players ako child nodes"""
	background_music = AudioStreamPlayer.new()
	background_music.name = "BackgroundMusic"
	background_music.bus = "Music"
	add_child(background_music)
	
	sfx_player = AudioStreamPlayer.new()
	sfx_player.name = "SFXPlayer"
	sfx_player.bus = "SFX"
	add_child(sfx_player)
	
	ui_player = AudioStreamPlayer.new()
	ui_player.name = "UIPlayer"
	ui_player.bus = "UI"
	add_child(ui_player)
	
	voice_player = AudioStreamPlayer.new()
	voice_player.name = "VoicePlayer"
	voice_player.bus = "Voice"
	add_child(voice_player)

	storm_player = AudioStreamPlayer.new()
	storm_player.name = "StormPlayer"
	storm_player.bus = "SFX"
	add_child(storm_player)

	# Vytvorenie atmosphere player
	atmosphere_player = AudioStreamPlayer.new()
	atmosphere_player.name = "AtmospherePlayer"
	atmosphere_player.bus = "SFX"
	add_child(atmosphere_player)

	print("✅ Audio players vytvorené")

func setup_audio_buses():
	"""Nastaví audio bus hierarchiu"""
	# AudioBus konfigurácia sa robí v Project Settings
	# Ale môžeme nastaviť volume levels programaticky
	
	# Nastavenie volume pre každý bus
	var music_bus_index = AudioServer.get_bus_index("Music")
	var sfx_bus_index = AudioServer.get_bus_index("SFX")
	var ui_bus_index = AudioServer.get_bus_index("UI")
	var voice_bus_index = AudioServer.get_bus_index("Voice")
	
	if music_bus_index != -1:
		AudioServer.set_bus_volume_db(music_bus_index, linear_to_db(music_volume))
		print("🎵 Music bus nastavený na: ", music_volume, " (", int(music_volume * 100), "%)")
	if sfx_bus_index != -1:
		AudioServer.set_bus_volume_db(sfx_bus_index, linear_to_db(sfx_volume))
	if ui_bus_index != -1:
		AudioServer.set_bus_volume_db(ui_bus_index, linear_to_db(ui_volume))
	if voice_bus_index != -1:
		AudioServer.set_bus_volume_db(voice_bus_index, linear_to_db(voice_volume))

	print("✅ Audio buses nastavené")

func load_audio_settings():
	"""Načíta uložené audio nastavenia"""
	# TODO: Implementovať načítanie z config súboru
	pass

func save_audio_settings():
	"""Uloží audio nastavenia"""
	# TODO: Implementovať uloženie do config súboru
	pass

func apply_current_music_volume():
	"""Aplikuje aktuálnu hlasitosť na hrajúcu hudbu"""
	if background_music and background_music.playing:
		# Kontrola či je to main menu hudba (50% hlasitosť)
		if current_track == "main_menu":
			background_music.volume_db = linear_to_db(music_volume * 0.5)
			print("🎵 Aplikovaná nová hlasitosť pre main menu: ", int(music_volume * 0.5 * 100), "%")
		else:
			background_music.volume_db = linear_to_db(music_volume)
			print("🎵 Aplikovaná nová hlasitosť pre hudbu: ", int(music_volume * 100), "%")

# =============================================================================
# MUSIC CONTROL FUNKCIE
# =============================================================================

func play_music(track_name: String, fade_in: bool = true):
	"""Spustí hudbu s možnosťou crossfade"""
	print("🎵 play_music() volaná s track_name: ", track_name)
	print("🎵 background_music existuje: ", background_music != null)

	if not audio_tracks.has(track_name):
		print("❌ Audio track neexistuje: ", track_name)
		print("❌ Dostupné tracky: ", audio_tracks.keys())
		return

	# Kontrola či už hrá rovnaký track a skutočne hrá
	if current_track == track_name and background_music.playing and background_music.stream != null:
		print("🎵 Track už hrá: ", track_name)
		return

	print("🎵 Spúšťam hudbu: ", track_name, " (predchádzajúca: ", current_track, ")")
	previous_track = current_track
	current_track = track_name

	# Načítanie MP3 súboru
	var audio_path = audio_tracks[track_name]
	print("🔍 Pokúšam sa načítať: ", audio_path)
	var audio_stream = load(audio_path)
	if not audio_stream:
		print("❌ Nemožno načítať audio súbor: ", audio_path)
		print("❌ Skontrolujte či súbor existuje a má správnu cestu")
		return
	else:
		print("✅ Audio súbor úspešne načítaný: ", audio_path)

	# Nastavenie loop pre background music
	if audio_stream is AudioStreamMP3:
		audio_stream.loop = true

	if fade_in and background_music.playing:
		print("🔄 Crossfade na nový track")
		crossfade_to_track(audio_stream)
	else:
		print("🎵 Nastavujem stream a spúšťam hudbu")
		background_music.stream = audio_stream

		# Nastavenie hlasitosti - main menu o 50% tichšie
		if track_name == "main_menu":
			background_music.volume_db = linear_to_db(music_volume * 0.5)  # 50% hlasitosť
			print("🎵 Main menu hudba nastavená na 50% hlasitosť")
		else:
			background_music.volume_db = linear_to_db(music_volume)  # Normálna hlasitosť

		background_music.play()
		print("🎵 background_music.playing =", background_music.playing)
		print("🎵 background_music.stream =", background_music.stream)

func crossfade_to_track(new_track: AudioStream):
	"""Plynulý prechod medzi trackmi"""
	print("🔄 crossfade_to_track() volaná")
	if is_crossfading:
		print("🔄 Už prebieha crossfade, používam priamy prechod")
		background_music.stop()
		background_music.stream = new_track
		background_music.play()
		return

	is_crossfading = true
	print("🔄 Začínam crossfade")

	# Jednoduchší crossfade bez await
	# Nastavenie cieľovej hlasitosti - main menu o 50% tichšie
	var target_volume = linear_to_db(music_volume)
	if current_track == "main_menu":
		target_volume = linear_to_db(music_volume * 0.5)  # 50% hlasitosť pre main menu

	# Fade out current track
	if background_music.playing:
		var fade_out_tween = create_tween()
		fade_out_tween.tween_property(background_music, "volume_db", -30, crossfade_time)

		# Po fade out prepnúť track
		fade_out_tween.tween_callback(func():
			print("🔄 Prepínam na nový track")
			background_music.stream = new_track
			background_music.volume_db = -30
			background_music.play()

			# Fade in nový track
			var fade_in_tween = create_tween()
			fade_in_tween.tween_property(background_music, "volume_db", target_volume, crossfade_time)
			fade_in_tween.tween_callback(func():
				print("🔄 Crossfade dokončený")
				is_crossfading = false
			)
		)
	else:
		# Ak nič nehrá, len spusti nový track
		background_music.stream = new_track
		background_music.volume_db = target_volume
		background_music.play()
		is_crossfading = false

func stop_music(fade_out: bool = true):
	"""Zastaví hudbu s možnosťou fade out"""
	if not background_music.playing:
		return

	if fade_out:
		var tween = create_tween()
		tween.tween_property(background_music, "volume_db", -30, 1.5)
		tween.tween_callback(func():
			background_music.stop()
			background_music.volume_db = linear_to_db(music_volume)
			current_track = ""
		)
	else:
		background_music.stop()
		current_track = ""

func stop_main_menu_music_immediately():
	"""Okamžite zastaví main menu hudbu bez fade-u"""
	if not background_music.playing or current_track != "main_menu":
		return

	print("🎵 Okamžite zastavujem main menu hudbu")
	background_music.stop()
	background_music.volume_db = linear_to_db(music_volume)
	current_track = ""

func pause_music():
	"""Pozastaví hudbu"""
	background_music.stream_paused = true

func resume_music():
	"""Obnoví hudbu"""
	background_music.stream_paused = false

func get_current_track() -> String:
	"""Vráti názov aktuálne hrajúceho tracku"""
	return current_track

func get_previous_track() -> String:
	"""Vráti názov predchádzajúceho tracku"""
	return previous_track

# =============================================================================
# KAPITOLA-SPECIFIC TRIGGERY
# =============================================================================

# Reset scene music map na pôvodné hodnoty
func reset_scene_music_map():
	"""Resetuje scene_music_map na pôvodné hodnoty"""
	scene_music_map = {
		1: "storm_journey",      # Kapitola 1
		2: "castle_gates",       # Kapitola 2
		3: "library_secrets",    # Kapitola 3
		4: "alchemy_lab",        # Kapitola 4
		5: "descent_darkness",   # Kapitola 5
		6: "isabelle_awakening", # Kapitola 6
		7: "van_helsing_rescue"  # Epilóg
	}
	print("🔄 Scene music map resetovaná na pôvodné hodnoty")

func stop_all_chapter_audio():
	"""Zastaví všetky audio efekty z predchádzajúcich kapitol"""
	print("🛑 Zastavujem všetky audio efekty z predchádzajúcich kapitol")

	# Zastaviť storm efekt
	if storm_player and storm_player.playing:
		print("⛈️ Zastavujem storm efekt")
		storm_player.stop()

	# Zastaviť atmosférické zvuky
	if atmosphere_player and atmosphere_player.playing:
		print("🌪️ Zastavujem atmosférický zvuk: ", current_atmosphere)
		atmosphere_player.stop()
		current_atmosphere = ""

	# Zastaviť SFX efekty (kroky a iné)
	if sfx_player and sfx_player.playing:
		print("👣 Zastavujem SFX efekty (kroky)")
		sfx_player.stop()

	# Resetovať atmosférické premenné
	atmosphere_volume = 0.0

	print("✅ Všetky audio efekty zastavené")

# Kapitola 1: Príchod do zámku
func start_chapter_1():
	"""Spustí hudbu pre kapitolu 1 - intro audio sa spúšťa v ChapterIntro"""
	print("🎵 start_chapter_1() volaná")

	# Okamžite zastav main menu hudbu
	stop_main_menu_music_immediately()

	# Zastaviť všetky audio efekty z predchádzajúcich kapitol
	stop_all_chapter_audio()

	reset_scene_music_map()

	# Spustí hudbu pre kapitolu 1 (intro audio sa už spustilo v ChapterIntro)
	play_music("storm_journey")

func start_storm_sequence():
	"""Spustí storm efekt s postupným znižovaním hlasitosti"""
	print("⛈️ Spúšťam storm sekvenciu pre kapitolu 1")

	# Načíta storm audio súbor
	var storm_audio = load("res://audio/effects/storm.wav")
	if not storm_audio:
		print("❌ Nemožno načítať storm.wav")
		# Fallback - spustí len hudbu
		play_music("storm_journey")
		return

	# Nastaví storm audio s loop
	if storm_audio is AudioStreamWAV:
		storm_audio.loop_mode = AudioStreamWAV.LOOP_FORWARD

	storm_player.stream = storm_audio
	storm_player.volume_db = linear_to_db(sfx_volume)  # Plná hlasitosť na začiatku
	storm_player.play()
	print("⛈️ Storm efekt spustený na plnú hlasitosť")

	# Vytvorí tween sekvenciu
	var storm_tween = create_tween()

	# 1. Čaká 5 sekúnd - storm hrá na plnú hlasitosť
	storm_tween.tween_interval(5.0)

	# 2. Po 5 sekundách spustí hudbu s crossfade
	storm_tween.tween_callback(func():
		print("🎵 Po 5 sekundách spúšťam storm_journey hudbu")
		play_music("storm_journey")
	)

	# 3. Súčasne začne znižovať hlasitosť storm efektu na 50%
	var target_storm_volume = linear_to_db(sfx_volume * 0.5)  # 50% z pôvodnej hlasitosti
	storm_tween.parallel().tween_property(storm_player, "volume_db", target_storm_volume, 3.0)

	print("⛈️ Storm sekvencia nastavená: 5s plná hlasitosť → hudba + zníženie na 50%")

func stop_storm_effect():
	"""Zastaví storm efekt s fade out"""
	if storm_player and storm_player.playing:
		print("⛈️ Zastavujem storm efekt")
		var fade_tween = create_tween()
		fade_tween.tween_property(storm_player, "volume_db", -30.0, 2.0)
		fade_tween.tween_callback(func():
			storm_player.stop()
			print("⛈️ Storm efekt zastavený")
		)

func enter_forest():
	"""Vstup do lesa - temná atmosféra"""
	print("🎵 enter_forest() volaná")
	# Storm efekt sa už nespúšťa v kapitole 1
	play_music("forest_shadows")

# Kapitola 2: Stopy krvi
func start_chapter_2():
	"""Spustí hudbu pre kapitolu 2 - brána zámku"""
	print("🎵 start_chapter_2() volaná")

	# Okamžite zastav main menu hudbu
	stop_main_menu_music_immediately()

	# Zastaviť všetky audio efekty z predchádzajúcich kapitol
	stop_all_chapter_audio()

	reset_scene_music_map()
	play_music("castle_gates")

func meet_viktor():
	"""Stretnutie s Viktorom - jeho téma"""
	# Aktualizovať mapu pre kapitolu 2 na Viktor's theme
	scene_music_map[2] = "viktor_theme"
	play_music("viktor_theme")

# Kapitola 3: Pátranie v zámku
func start_chapter_3():
	"""Spustí hudbu pre kapitolu 3 - knižnica tajomstiev"""
	# Okamžite zastav main menu hudbu
	stop_main_menu_music_immediately()

	# Zastaviť všetky audio efekty z predchádzajúcich kapitol
	stop_all_chapter_audio()

	reset_scene_music_map()
	play_music("library_secrets")

# Kapitola 4: Tajné krídlo
func start_chapter_4():
	"""Spustí hudbu pre kapitolu 4 - alchymistické laboratórium"""
	# Okamžite zastav main menu hudbu
	stop_main_menu_music_immediately()

	# Zastaviť všetky audio efekty z predchádzajúcich kapitol
	stop_all_chapter_audio()

	reset_scene_music_map()
	play_music("alchemy_lab")

# Kapitola 5: Krypty
func start_chapter_5():
	"""Spustí hudbu pre kapitolu 5 - zostup do temnoty"""
	# Okamžite zastav main menu hudbu
	stop_main_menu_music_immediately()

	# Zastaviť všetky audio efekty z predchádzajúcich kapitol
	stop_all_chapter_audio()

	reset_scene_music_map()
	play_music("descent_darkness")

func enter_crypts():
	"""Vstup do krypt - pradávne hrobky"""
	# Aktualizovať mapu pre kapitolu 5 na ancient_crypts
	scene_music_map[5] = "ancient_crypts"
	play_music("ancient_crypts")

# Kapitola 6: Konfrontácia
func start_chapter_6():
	"""Spustí hudbu pre kapitolu 6 - prebudenie Isabelle"""
	# Okamžite zastav main menu hudbu
	stop_main_menu_music_immediately()

	# Zastaviť všetky audio efekty z predchádzajúcich kapitol
	stop_all_chapter_audio()

	reset_scene_music_map()
	play_music("isabelle_awakening")

func final_battle():
	"""Finálny boj - posledný rituál"""
	# Aktualizovať mapu pre kapitolu 6 na final ritual
	scene_music_map[6] = "final_ritual"
	play_music("final_ritual")

# Kapitola 7: Epilóg
func start_chapter_7():
	"""Spustí hudbu pre kapitolu 7 - záchrana Van Helsinga"""
	# Okamžite zastav main menu hudbu
	stop_main_menu_music_immediately()

	# Zastaviť všetky audio efekty z predchádzajúcich kapitol
	stop_all_chapter_audio()

	reset_scene_music_map()
	play_music("van_helsing_rescue")

# Epilóg (alias pre kapitolu 7)
func start_epilogue():
	"""Spustí hudbu pre epilóg - záchrana Van Helsinga"""
	start_chapter_7()

# Puzzle system - jednoduchý systém s pamätaním hudby
func start_puzzle():
	"""Spustí hudbu pre hlavolamy a zapamätá si predchádzajúcu"""
	print("🧩 Spúšťam puzzle hudbu, predchádzajúca: ", current_track)
	play_music("puzzle_theme")

func return_from_puzzle():
	"""Vráti sa k predchádzajúcej hudbe po dokončení puzzle"""
	print("🔙 Návrat z puzzle, predchádzajúca hudba: ", previous_track)
	print("🔙 Aktuálna kapitola: ", GameManager.current_chapter)

	# Ak máme zapamätanú predchádzajúcu hudbu a nie je to puzzle theme
	if previous_track != "" and previous_track != "puzzle_theme":
		print("✅ Obnovujem predchádzajúcu hudbu: ", previous_track)
		play_music(previous_track)
	else:
		# Fallback - pokúsiť sa určiť hudbu podľa aktuálnej kapitoly
		var current_chapter = GameManager.current_chapter
		print("🔍 Hľadám hudbu pre kapitolu ", current_chapter, " v scene_music_map")

		if scene_music_map.has(current_chapter):
			var chapter_music = scene_music_map[current_chapter]
			print("🎵 Fallback na hudbu kapitoly ", current_chapter, ": ", chapter_music)
			play_music(chapter_music)
		else:
			# Posledný fallback - určiť hudbu podľa čísla kapitoly
			print("⚠️ scene_music_map nemá kapitolu ", current_chapter, ", používam manuálny fallback")
			match current_chapter:
				1:
					play_music("storm_journey")
				2:
					play_music("castle_gates")
				3:
					play_music("library_secrets")
				4:
					play_music("alchemy_lab")
				5:
					play_music("descent_darkness")
				6:
					play_music("isabelle_awakening")
				7:
					play_music("van_helsing_rescue")
				_:
					print("⚠️ Neznáma kapitola, používam library_secrets")
					play_music("library_secrets")

# =============================================================================
# VOLUME CONTROL FUNKCIE
# =============================================================================

func set_music_volume(value: float):
	"""Nastaví hlasitosť hudby (0.0 - 1.0)"""
	music_volume = clamp(value, 0.0, 1.0)
	var music_bus_index = AudioServer.get_bus_index("Music")
	if music_bus_index != -1:
		AudioServer.set_bus_volume_db(music_bus_index, linear_to_db(music_volume))
	save_audio_settings()

func set_sfx_volume(value: float):
	"""Nastaví hlasitosť SFX (0.0 - 1.0)"""
	sfx_volume = clamp(value, 0.0, 1.0)
	var sfx_bus_index = AudioServer.get_bus_index("SFX")
	if sfx_bus_index != -1:
		AudioServer.set_bus_volume_db(sfx_bus_index, linear_to_db(sfx_volume))
	save_audio_settings()

func set_ui_volume(value: float):
	"""Nastaví hlasitosť UI (0.0 - 1.0)"""
	ui_volume = clamp(value, 0.0, 1.0)
	var ui_bus_index = AudioServer.get_bus_index("UI")
	if ui_bus_index != -1:
		AudioServer.set_bus_volume_db(ui_bus_index, linear_to_db(ui_volume))
	save_audio_settings()

func set_voice_volume(value: float):
	"""Nastaví hlasitosť hlasu (0.0 - 1.0)"""
	voice_volume = clamp(value, 0.0, 1.0)
	var voice_bus_index = AudioServer.get_bus_index("Voice")
	if voice_bus_index != -1:
		AudioServer.set_bus_volume_db(voice_bus_index, linear_to_db(voice_volume))
	save_audio_settings()

func get_music_volume() -> float:
	"""Vráti aktuálnu hlasitosť hudby"""
	return music_volume

func get_sfx_volume() -> float:
	"""Vráti aktuálnu hlasitosť SFX"""
	return sfx_volume

func get_ui_volume() -> float:
	"""Vráti aktuálnu hlasitosť UI"""
	return ui_volume

func get_voice_volume() -> float:
	"""Vráti aktuálnu hlasitosť hlasu"""
	return voice_volume

# =============================================================================
# SFX FUNKCIE
# =============================================================================

func play_sfx(sound_path: String):
	"""Prehrá sound effect"""
	var audio_stream = load(sound_path)
	if audio_stream:
		sfx_player.stream = audio_stream
		sfx_player.play()

func play_ui_sound(sound_path: String):
	"""Prehrá UI sound"""
	var audio_stream = load(sound_path)
	if audio_stream:
		ui_player.stream = audio_stream
		ui_player.play()

# =============================================================================
# UI ZVUKY
# =============================================================================

func play_menu_button_sound():
	"""Prehrá zvuk pri stlačení button v main menu"""
	play_ui_sound("res://audio/UI_SOUNDS/menu_sound.wav")

func play_puzzle_success_sound():
	"""Prehrá zvuk pri správnom vyriešení hádanky"""
	play_ui_sound("res://audio/UI_SOUNDS/správne.wav")

func play_puzzle_error_sound():
	"""Prehrá zvuk pri nesprávnej odpovedi v hádanke"""
	play_ui_sound("res://audio/UI_SOUNDS/nesprávne.wav")

func play_game_start_sound():
	"""Prehrá zvuk pri začiatku novej hry"""
	play_ui_sound("res://audio/UI_SOUNDS/začiatok.wav")

func play_lightning_sound():
	"""Prehrá zvuk blesku"""
	play_sfx("res://audio/effects/Blesk.wav")

# =============================================================================
# ATMOSFÉRICKÉ ZVUKY - KAPITOLY 1-3
# =============================================================================

func play_atmosphere(sound_name: String, volume_percent: float = 70.0, loop: bool = true):
	"""Prehrá atmosférický zvuk s nastavenou hlasitosťou"""
	var sound_path = ""

	match sound_name:
		"storm":
			sound_path = "res://audio/effects/storm.mp3"
		"brána", "brana":
			sound_path = "res://audio/effects/brána.mp3"
		"vstupná_hala", "vstupna_hala":
			sound_path = "res://audio/effects/Vstupná hala.mp3"
		_:
			print("❌ Neznámy atmosférický zvuk: ", sound_name)
			return

	print("🌪️ Spúšťam atmosférický zvuk: ", sound_name, " na ", volume_percent, "%")

	var audio_stream = load(sound_path)
	if not audio_stream:
		print("❌ Nemožno načítať atmosférický zvuk: ", sound_path)
		return

	# Nastavenie loop
	if audio_stream is AudioStreamMP3:
		audio_stream.loop = loop
	elif audio_stream is AudioStreamWAV:
		if loop:
			audio_stream.loop_mode = AudioStreamWAV.LOOP_FORWARD
		else:
			audio_stream.loop_mode = AudioStreamWAV.LOOP_DISABLED

	# Prehranie s nastavenou hlasitosťou
	atmosphere_player.stream = audio_stream
	atmosphere_player.volume_db = linear_to_db(sfx_volume * (volume_percent / 100.0))
	atmosphere_player.play()

	current_atmosphere = sound_name
	atmosphere_volume = volume_percent / 100.0

func play_atmosphere_with_fade(sound_name: String, target_volume_percent: float = 70.0, loop: bool = true):
	"""Prehrá atmosférický zvuk okamžite s fade efektom od 0 do cieľovej hlasitosti"""
	var sound_path = ""

	match sound_name:
		"storm":
			sound_path = "res://audio/effects/storm.mp3"
		"brána", "brana":
			sound_path = "res://audio/effects/brána.mp3"
		"vstupná_hala", "vstupna_hala":
			sound_path = "res://audio/effects/Vstupná hala.mp3"
		_:
			print("❌ Neznámy atmosférický zvuk: ", sound_name)
			return

	print("🌪️ Spúšťam atmosférický zvuk s fade: ", sound_name, " na ", target_volume_percent, "%")

	var audio_stream = load(sound_path)
	if not audio_stream:
		print("❌ Nemožno načítať atmosférický zvuk: ", sound_path)
		return

	# Nastavenie loop
	if audio_stream is AudioStreamMP3:
		audio_stream.loop = loop
	elif audio_stream is AudioStreamWAV:
		if loop:
			audio_stream.loop_mode = AudioStreamWAV.LOOP_FORWARD
		else:
			audio_stream.loop_mode = AudioStreamWAV.LOOP_DISABLED

	# Spustenie na tichú hlasitosť
	atmosphere_player.stream = audio_stream
	atmosphere_player.volume_db = -80.0  # Veľmi ticho
	atmosphere_player.play()

	# Fade in efekt na cieľovú hlasitosť
	var target_db = linear_to_db(sfx_volume * (target_volume_percent / 100.0))
	var fade_tween = create_tween()
	fade_tween.tween_property(atmosphere_player, "volume_db", target_db, 2.0)  # 2 sekundy fade

	current_atmosphere = sound_name
	atmosphere_volume = target_volume_percent / 100.0

	print("⛈️ Fade in efekt spustený na ", target_volume_percent, "% za 2 sekundy")

	print("✅ Atmosférický zvuk spustený: ", sound_name)

func change_atmosphere_volume(volume_percent: float, fade_time: float = 2.0):
	"""Zmení hlasitosť atmosférického zvuku s fade efektom"""
	if not atmosphere_player.playing:
		print("⚠️ Žiadny atmosférický zvuk nehrá")
		return

	var target_volume = linear_to_db(sfx_volume * (volume_percent / 100.0))
	print("🔊 Mením hlasitosť atmosféry z ", atmosphere_volume * 100, "% na ", volume_percent, "%")

	var tween = create_tween()
	tween.tween_property(atmosphere_player, "volume_db", target_volume, fade_time)

	atmosphere_volume = volume_percent / 100.0

func stop_atmosphere(fade_time: float = 2.0):
	"""Zastaví atmosférický zvuk s fade out"""
	if not atmosphere_player.playing:
		return

	print("🌪️ Zastavujem atmosférický zvuk: ", current_atmosphere)

	var tween = create_tween()
	tween.tween_property(atmosphere_player, "volume_db", -30.0, fade_time)
	tween.tween_callback(func():
		atmosphere_player.stop()
		current_atmosphere = ""
		print("✅ Atmosférický zvuk zastavený")
	)

func crossfade_atmosphere(new_sound: String, volume_percent: float = 70.0, fade_time: float = 3.0):
	"""Crossfade medzi dvoma atmosférickými zvukmi"""
	print("🔄 Crossfade atmosféry: ", current_atmosphere, " → ", new_sound)

	if atmosphere_player.playing:
		# Fade out súčasný zvuk
		var fade_out_tween = create_tween()
		fade_out_tween.tween_property(atmosphere_player, "volume_db", -30.0, fade_time)
		fade_out_tween.tween_callback(func():
			atmosphere_player.stop()
			# Spustiť nový zvuk
			play_atmosphere(new_sound, volume_percent)
		)
	else:
		# Ak nič nehrá, len spusti nový zvuk
		play_atmosphere(new_sound, volume_percent)

# =============================================================================
# KAPITOLA-ŠPECIFICKÉ ATMOSFÉRICKÉ FUNKCIE
# =============================================================================

# KAPITOLA 1 - STORM SEKVENCIA
func start_storm_atmosphere():
	"""Spustí storm atmosféru pre kapitolu 1 okamžite s fade efektom na 28% (znížené o 35%)"""
	print("⛈️ KAPITOLA 1: Spúšťam storm atmosféru okamžite s fade na 28%")
	play_atmosphere_with_fade("storm", 28.0)

func storm_narrator_start():
	"""[ROZPRÁVAČ_001] - Znížiť storm na 23% (znížené o 35% z 35%)"""
	print("⛈️ ROZPRÁVAČ_001: Znižujem storm na 23%")
	change_atmosphere_volume(23.0, 2.0)

func storm_chaos():
	"""[ROZPRÁVAČ_006] - Zvýšiť storm na 28% (znížené o 35% z 42.5%)"""
	print("⛈️ ROZPRÁVAČ_006: Zvyšujem storm na 28% (chaos)")
	change_atmosphere_volume(28.0, 1.5)

func storm_forest():
	"""[ROZPRÁVAČ_015] - Znížiť storm na 13% (znížené o 35% z 20%)"""
	print("⛈️ ROZPRÁVAČ_015: Znižujem storm na 13% (les)")
	change_atmosphere_volume(13.0, 3.0)

func storm_fade_out():
	"""[ROZPRÁVAČ_022] - Postupne umlčať storm (pred bránou)"""
	print("⛈️ ROZPRÁVAČ_022: Postupne umlčavam storm (brána)")
	stop_atmosphere(5.0)

func start_footsteps():
	"""Spustí zvuk krokov z audio/effects/kroky.mp3"""
	print("👣 Spúšťam zvuk krokov...")
	play_sfx("res://audio/effects/kroky.mp3")
	print("👣 Kroky spustené")

func storm_to_footsteps():
	"""Vypne búrku s fade out a spustí kroky - pre prechod pred druhou hádankou v kapitole 1"""
	print("⛈️➡️👣 Prechod z búrky na kroky pred druhou hádankou")

	# Vypnúť búrku s fade out
	storm_fade_out()

	# Po 2 sekundách spustiť kroky (aby sa búrka stihla stíšiť)
	var timer = get_tree().create_timer(2.0)
	timer.timeout.connect(start_footsteps)
	print("⏰ Timer nastavený na 2 sekundy pre spustenie krokov")

# KAPITOLA 2 - BRÁNA SEKVENCIA
func start_gate_atmosphere():
	"""[ROZPRÁVAČ_022] - Spustiť BRÁNA na 60%"""
	print("🚪 ROZPRÁVAČ_022: Spúšťam bránu na 60%")
	play_atmosphere("brana", 60.0)

func gate_ravens():
	"""[ROZPRÁVAČ_025] - Pridať zdôraznenie (havrany krákajú)"""
	print("🚪 ROZPRÁVAČ_025: Zdôrazňujem bránu (havrany)")
	change_atmosphere_volume(70.0, 1.0)

func gate_puzzle_emphasis():
	"""[HLAVOLAM_3_UVOD] - Mierne zvýšiť hlasitosť na 75%"""
	print("🚪 HLAVOLAM_3_UVOD: Zvyšujem bránu na 75%")
	change_atmosphere_volume(75.0, 2.0)

func gate_opening():
	"""[ROZPRÁVAČ_026] - Dramatický moment (brána sa otvára)"""
	print("🚪 ROZPRÁVAČ_026: Dramatický moment (brána sa otvára)")
	change_atmosphere_volume(80.0, 1.0)

func gate_courtyard():
	"""[ROZPRÁVAČ_028] - Prechod na nádvorie, udržať ako podklad"""
	print("🚪 ROZPRÁVAČ_028: Prechod na nádvorie (podklad)")
	change_atmosphere_volume(50.0, 2.0)

# KAPITOLA 3 - VSTUPNÁ HALA SEKVENCIA
func start_hall_atmosphere():
	"""[ROZPRÁVAČ_030] - Spustiť VSTUPNÁ HALA na 70%"""
	print("🏰 ROZPRÁVAČ_030: Spúšťam vstupnú halu na 70%")
	crossfade_atmosphere("vstupna_hala", 70.0, 3.0)

func hall_clock_emphasis():
	"""[ROZPRÁVAČ_032] - Zdôrazniť tikanie hodín (layering)"""
	print("🏰 ROZPRÁVAČ_032: Zdôrazňujem tikanie hodín")
	change_atmosphere_volume(80.0, 1.5)

func hall_library_transition():
	"""[ROZPRÁVAČ_033] - Znížiť na 30% (prechod do knižnice)"""
	print("🏰 ROZPRÁVAČ_033: Znižujem na 30% (knižnica)")
	change_atmosphere_volume(30.0, 2.0)

func hall_stop():
	"""[ROZPRÁVAČ_038] - Úplne umlčať (vstup do starého krídla)"""
	print("🏰 ROZPRÁVAČ_038: Umlčavam halu (staré krídlo)")
	stop_atmosphere(3.0)
