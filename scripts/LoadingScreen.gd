extends Control

# Jednoduch<PERSON> loading scéna, ktorá sa zobrazí okamžite a zabráni sivej obrazovke

@onready var logo: TextureRect = $LogoContainer/Logo
@onready var fade_overlay: ColorRect = $FadeOverlay

func _ready():
	print("🔄 LoadingScreen: Okamžité načítanie")

	# Nastaviť počiatočný stav
	logo.modulate.a = 0.0
	fade_overlay.color.a = 1.0

	# Okamžite spustiť fade in animáciu
	start_fade_in()

	# Po krátkom čase prejsť na SplashScreen
	var timer = get_tree().create_timer(0.5)
	timer.timeout.connect(_start_fade_out)

func start_fade_in():
	"""Rýchky fade in efekt"""
	var tween = create_tween()
	tween.set_parallel(true)
	tween.tween_property(fade_overlay, "color:a", 0.0, 0.2)
	tween.tween_property(logo, "modulate:a", 1.0, 0.3)

func _start_fade_out():
	"""Spustí fade out"""
	fade_out()

func fade_out():
	"""Krátky fade out pred prechodom"""
	var tween = create_tween()
	tween.tween_property(fade_overlay, "color:a", 1.0, 0.2)
	tween.finished.connect(load_splash_screen)

func load_splash_screen():
	"""Načíta SplashScreen scénu"""
	print("🔄 LoadingScreen: Prechod na SplashScreen")
	get_tree().change_scene_to_file("res://scenes/SplashScreen.tscn")
