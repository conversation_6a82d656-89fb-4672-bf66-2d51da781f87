// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
        054F8BE62D38852F00B81423 /* MetalFX.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 054F8BE52D38852F00B81423 /* MetalFX.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		1F1575721F582BE20003B888 /* dylibs in Resources */ = {isa = PBXBuildFile; fileRef = 1F1575711F582BE20003B888 /* dylibs */; };
		DEADBEEF2F582BE20003B888 /* 555.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = DEADBEEF1F582BE20003B888 /* 555.xcframework */; };
		
		
		1FF8DBB11FBA9DE1009DE660 /* dummy.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1FF8DBB01FBA9DE1009DE660 /* dummy.cpp */; };
		D07CD44E1C5D589C00B7FB28 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = D07CD44D1C5D589C00B7FB28 /* Images.xcassets */; };
		9039D3BE24C093AC0020482C /* MoltenVK.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9039D3BD24C093AC0020482C /* MoltenVK.xcframework */; };
		D0BCFE4618AEBDA2004A7AAE /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = D0BCFE4418AEBDA2004A7AAE /* InfoPlist.strings */; };
		D0BCFE7818AEBFEB004A7AAE /* 555.pck in Resources */ = {isa = PBXBuildFile; fileRef = D0BCFE7718AEBFEB004A7AAE /* 555.pck */; };
		F965960D2BC2C3A800579C7E /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = F965960C2BC2C3A800579C7E /* PrivacyInfo.xcprivacy */; };
		90DD2D9E24B36E8000717FE1 /* Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 90DD2D9D24B36E8000717FE1 /* Launch Screen.storyboard */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		90A13CD024AA68E500E8464F /* Embed Frameworks */ = {
				isa = PBXCopyFilesBuildPhase;
				buildActionMask = 2147483647;
				dstPath = "";
				dstSubfolderSpec = 10;
				files = (
					
				);
				name = "Embed Frameworks";
				runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		1F1575711F582BE20003B888 /* dylibs */ = {isa = PBXFileReference; lastKnownFileType = folder; name = dylibs; path = "555/dylibs"; sourceTree = "<group>"; };
		DEADBEEF1F582BE20003B888 /* 555.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = godot; path = "555.xcframework"; sourceTree = "<group>"; };
		
		
		1FF4C1881F584E6300A41E41 /* 555.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "555.entitlements"; sourceTree = "<group>"; };
		1FF8DBB01FBA9DE1009DE660 /* dummy.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = dummy.cpp; sourceTree = "<group>"; };
		9039D3BD24C093AC0020482C /* MoltenVK.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = MoltenVK; path = MoltenVK.xcframework; sourceTree = "<group>"; };
		054F8BE52D38852F00B81423 /* MetalFX.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalFX.framework; path = System/Library/Frameworks/MetalFX.framework; sourceTree = SDKROOT; };
		D07CD44D1C5D589C00B7FB28 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		D0BCFE3418AEBDA2004A7AAE /* 555.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "555.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		D0BCFE4318AEBDA2004A7AAE /* 555-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "555-Info.plist"; sourceTree = "<group>"; };
		D0BCFE4518AEBDA2004A7AAE /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
				D0BCFE7718AEBFEB004A7AAE /* 555.pck */ = {isa = PBXFileReference; lastKnownFileType = file; path = "555.pck"; sourceTree = "<group>"; };
		F965960C2BC2C3A800579C7E /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		90DD2D9D24B36E8000717FE1 = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = "Launch Screen.storyboard"; sourceTree = "<group>"; };
/* End PBXFileReference section */

		

/* Begin PBXFrameworksBuildPhase section */
		D0BCFE3118AEBDA2004A7AAE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9039D3BE24C093AC0020482C /* MoltenVK.xcframework in Frameworks */,
				054F8BE62D38852F00B81423 /* MetalFX.framework in Frameworks */,
				DEADBEEF2F582BE20003B888 /* 555.xcframework */,
				
				
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		D0BCFE2B18AEBDA2004A7AAE = {
			isa = PBXGroup;
			children = (
				1F1575711F582BE20003B888 /* dylibs */,
				D0BCFE7718AEBFEB004A7AAE /* 555.pck */,
				D0BCFE4118AEBDA2004A7AAE /* 555 */,
				D0BCFE3618AEBDA2004A7AAE /* Frameworks */,
				D0BCFE3518AEBDA2004A7AAE /* Products */,
				F965960C2BC2C3A800579C7E /* PrivacyInfo.xcprivacy */,
				
			);
			sourceTree = "<group>";
		};
		D0BCFE3518AEBDA2004A7AAE /* Products */ = {
			isa = PBXGroup;
			children = (
				D0BCFE3418AEBDA2004A7AAE /* 555.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		D0BCFE3618AEBDA2004A7AAE /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				9039D3BD24C093AC0020482C /* MoltenVK.xcframework */,
				054F8BE52D38852F00B81423 /* MetalFX.framework */,
				DEADBEEF1F582BE20003B888 /* 555.xcframework */,
				
				
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		D0BCFE4118AEBDA2004A7AAE /* 555 */ = {
			isa = PBXGroup;
			children = (
				90DD2D9D24B36E8000717FE1 /* Launch Screen.storyboard */,
				1FF4C1881F584E6300A41E41 /* 555.entitlements */,
				D07CD44D1C5D589C00B7FB28 /* Images.xcassets */,
				D0BCFE4218AEBDA2004A7AAE /* Supporting Files */,
				1FF8DBB01FBA9DE1009DE660 /* dummy.cpp */,
				
			);
			path = "555";
			sourceTree = "<group>";
		};
		D0BCFE4218AEBDA2004A7AAE /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				D0BCFE4318AEBDA2004A7AAE /* 555-Info.plist */,
				D0BCFE4418AEBDA2004A7AAE /* InfoPlist.strings */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		D0BCFE3318AEBDA2004A7AAE /* 555 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D0BCFE7118AEBDA3004A7AAE /* Build configuration list for PBXNativeTarget "555" */;
			buildPhases = (
				D0BCFE3018AEBDA2004A7AAE /* Sources */,
				D0BCFE3118AEBDA2004A7AAE /* Frameworks */,
				D0BCFE3218AEBDA2004A7AAE /* Resources */,
				90A13CD024AA68E500E8464F /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "555";
			productName = "Van Helsing: Prekliate Dedičstvo";
			productReference = D0BCFE3418AEBDA2004A7AAE /* 555.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		D0BCFE2C18AEBDA2004A7AAE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0500;
				ORGANIZATIONNAME = GodotEngine;
				TargetAttributes = {
					D0BCFE3318AEBDA2004A7AAE = {
						DevelopmentTeam = F3RYXV7KPQ;
						
						ProvisioningStyle = Automatic;
						SystemCapabilities = {
						};
					};
				};
			};
			buildConfigurationList = D0BCFE2F18AEBDA2004A7AAE /* Build configuration list for PBXProject "555" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = D0BCFE2B18AEBDA2004A7AAE;
			productRefGroup = D0BCFE3518AEBDA2004A7AAE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				D0BCFE3318AEBDA2004A7AAE /* 555 */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		D0BCFE3218AEBDA2004A7AAE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D07CD44E1C5D589C00B7FB28 /* Images.xcassets in Resources */,
				D0BCFE7818AEBFEB004A7AAE /* 555.pck in Resources */,
				90DD2D9E24B36E8000717FE1 /* Launch Screen.storyboard in Resources */,
				D0BCFE4618AEBDA2004A7AAE /* InfoPlist.strings in Resources */,
				F965960D2BC2C3A800579C7E /* PrivacyInfo.xcprivacy in Resources */,
				
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		D0BCFE3018AEBDA2004A7AAE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1FF8DBB11FBA9DE1009DE660 /* dummy.cpp in Sources */,
				
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		D0BCFE4418AEBDA2004A7AAE /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				D0BCFE4518AEBDA2004A7AAE /* en */,
							);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		D0BCFE6F18AEBDA3004A7AAE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "arm64";
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_BITCODE = NO;
				"FRAMEWORK_SEARCH_PATHS[arch=*]" = (
					"$(PROJECT_DIR)/**",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				"LD_CLASSIC_1500" = "-ld_classic";
				"LD_CLASSIC_1501" = "-ld_classic";
				"LD_CLASSIC_1510" = "-ld_classic";
				OTHER_LDFLAGS = "$(LD_CLASSIC_$(XCODE_VERSION_ACTUAL))  ";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		D0BCFE7018AEBDA3004A7AAE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "arm64";
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				COPY_PHASE_STRIP = YES;
				ENABLE_BITCODE = NO;
				"FRAMEWORK_SEARCH_PATHS[arch=*]" = (
					"$(PROJECT_DIR)/**",
				);
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				"LD_CLASSIC_1500" = "-ld_classic";
				"LD_CLASSIC_1501" = "-ld_classic";
				"LD_CLASSIC_1510" = "-ld_classic";
				OTHER_LDFLAGS = "$(LD_CLASSIC_$(XCODE_VERSION_ACTUAL))  ";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		D0BCFE7218AEBDA3004A7AAE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "arm64";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = "555/555.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = "Automatic";
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)";
				DEVELOPMENT_TEAM = F3RYXV7KPQ;
				INFOPLIST_FILE = "555/555-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/**",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.prekliatededicstvo.game;
				INFOPLIST_KEY_CFBundleDisplayName = "Van Helsing: Prekliate Dedičstvo";
				PRODUCT_NAME = "555";
				EXECUTABLE_NAME = "555";
				MARKETING_VERSION = 1.0;
				CURRENT_PROJECT_VERSION = 1.0;
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = "arm64 x86_64";
				WRAPPER_EXTENSION = app;
				
			};
			name = Debug;
		};
		D0BCFE7318AEBDA3004A7AAE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "arm64";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = "555/555.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = "Automatic";
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)";
				DEVELOPMENT_TEAM = F3RYXV7KPQ;
				INFOPLIST_FILE = "555/555-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/**",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.prekliatededicstvo.game;
				INFOPLIST_KEY_CFBundleDisplayName = "Van Helsing: Prekliate Dedičstvo";
				PRODUCT_NAME = "555";
				EXECUTABLE_NAME = "555";
				MARKETING_VERSION = 1.0;
				CURRENT_PROJECT_VERSION = 1.0;
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = "arm64 x86_64";
				WRAPPER_EXTENSION = app;
				
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		D0BCFE2F18AEBDA2004A7AAE /* Build configuration list for PBXProject "555" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D0BCFE6F18AEBDA3004A7AAE /* Debug */,
				D0BCFE7018AEBDA3004A7AAE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		D0BCFE7118AEBDA3004A7AAE /* Build configuration list for PBXNativeTarget "555" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D0BCFE7218AEBDA3004A7AAE /* Debug */,
				D0BCFE7318AEBDA3004A7AAE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = D0BCFE2C18AEBDA2004A7AAE /* Project object */;
}

